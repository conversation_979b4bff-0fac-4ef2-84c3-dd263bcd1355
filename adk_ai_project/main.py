import asyncio
import os
from functools import partial
from dotenv import load_dotenv

from google.adk.agents import LlmAgent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types

from .agents.utility_agent import DialogueUtilityAgent
from .callbacks.history_callbacks import (
    before_main_model_callback,
    after_main_model_callback,
)
from .agents.mentor_agent import ZhishenMentorAgent
from .pipeline import ZhishenMentorPipeline
from .prompts import MentorPrompts


load_dotenv()


def create_main_agent() -> LlmAgent:
    """
    创建并配置主 LlmAgent。
    """
    # 1) 创建工具 Agent 单例
    utility_agent = DialogueUtilityAgent()

    # 2) 绑定 after 回调
    bound_after_callback = partial(after_main_model_callback, utility_agent=utility_agent)

    # 3) 创建核心 MentorAgent（通用指令，不含具体知识），从集中 prompts 加载
    mentor_agent = ZhishenMentorAgent(instruction=MentorPrompts.GENERAL_PERSONA)
    mentor_agent.before_model_callback = before_main_model_callback
    mentor_agent.after_model_callback = bound_after_callback

    # 4) 创建顶层 Pipeline Agent
    pipeline_agent = ZhishenMentorPipeline(mentor_agent=mentor_agent)
    return pipeline_agent


async def main():
    agent = create_main_agent()

    app_name = "adk_zhishen_mentor"
    user_id = "test_user_01"
    session_id = "test_session_01"

    session_service = InMemorySessionService()
    runner = Runner(agent=agent, app_name=app_name, session_service=session_service)

    await session_service.create_session(
        app_name=app_name, user_id=user_id, session_id=session_id
    )
    print("知深导师已准备就绪，输入 'exit' 退出。")

    while True:
        try:
            user_input = await asyncio.to_thread(input, "🧑‍💻 User: ")
            if user_input.lower() == "exit":
                print("对话结束。")
                break

            user_content = types.Content(role="user", parts=[types.Part(text=user_input)])
            print("🤖 Assistant: ", end="", flush=True)
            async for event in runner.run_async(
                user_id=user_id, session_id=session_id, new_message=user_content
            ):
                if event.is_model_response() and event.content and event.content.parts:
                    print(event.content.parts[0].text, end="", flush=True)
            print("\n")
        except (KeyboardInterrupt, EOFError):
            print("\n对话结束。")
            break
        except Exception as e:
            print(f"\n发生错误: {e}")
            break


root_agent = create_main_agent()

if __name__ == "__main__":
    if os.name == "nt":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())
